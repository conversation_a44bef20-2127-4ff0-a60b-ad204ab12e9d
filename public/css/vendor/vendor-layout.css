/* ===== ULTRA MODERN VENDOR LAYOUT CSS ===== */
/* Completely redesigned modern and unique vendor layout */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* === CSS VARIABLES === */
:root {
    /* Simple Light Green Color Palette */
    --vendor-primary: #66BB6A;        /* Medium Light Green */
    --vendor-primary-dark: #66BB6A;   /* Same Medium Light Green */
    --vendor-primary-light: rgba(102, 187, 106, 0.1);
    --vendor-accent: #66BB6A;         /* Same Medium Light Green */
    --vendor-accent-light: rgba(102, 187, 106, 0.1);
    --vendor-warning: #F59E0B;
    --vendor-danger: #EF4444;
    --vendor-success: #66BB6A;        /* Medium Light Green */

    /* Neutral Colors */
    --vendor-gray-50: #F9FAFB;
    --vendor-gray-100: #F3F4F6;
    --vendor-gray-200: #E5E7EB;
    --vendor-gray-300: #D1D5DB;
    --vendor-gray-400: #9CA3AF;
    --vendor-gray-500: #6B7280;
    --vendor-gray-600: #4B5563;
    --vendor-gray-700: #374151;
    --vendor-gray-800: #1F2937;
    --vendor-gray-900: #111827;

    /* Background Colors - Light Green Inspired */
    --vendor-bg: #F8FDF8;             /* Very light green background */
    --vendor-bg-secondary: rgba(102, 187, 106, 0.05);
    --vendor-surface: #FFFFFF;
    --vendor-surface-elevated: #FFFFFF;

    /* Text Colors */
    --vendor-text-primary: #1F2937;
    --vendor-text-secondary: #4B5563;
    --vendor-text-muted: #6B7280;
    --vendor-text-light: #9CA3AF;

    /* Border & Effects */
    --vendor-border: #E5E7EB;
    --vendor-border-light: #F3F4F6;
    --vendor-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --vendor-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --vendor-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --vendor-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --vendor-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Layout Dimensions */
    --vendor-sidebar-width: 280px;
    --vendor-header-height: 72px;

    /* Typography */
    --vendor-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --vendor-font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    --vendor-font-size-xs: 0.75rem;
    --vendor-font-size-sm: 0.875rem;
    --vendor-font-size-base: 1rem;
    --vendor-font-size-lg: 1.125rem;
    --vendor-font-size-xl: 1.25rem;
    --vendor-font-size-2xl: 1.5rem;
    --vendor-font-size-3xl: 1.875rem;
    --vendor-font-size-4xl: 2.25rem;

    /* Spacing Scale */
    --vendor-space-0: 0;
    --vendor-space-1: 0.25rem;
    --vendor-space-2: 0.5rem;
    --vendor-space-3: 0.75rem;
    --vendor-space-4: 1rem;
    --vendor-space-5: 1.25rem;
    --vendor-space-6: 1.5rem;
    --vendor-space-8: 2rem;
    --vendor-space-10: 2.5rem;
    --vendor-space-12: 3rem;
    --vendor-space-16: 4rem;
    --vendor-space-20: 5rem;

    /* Border Radius */
    --vendor-radius-sm: 0.375rem;
    --vendor-radius: 0.5rem;
    --vendor-radius-md: 0.625rem;
    --vendor-radius-lg: 0.75rem;
    --vendor-radius-xl: 1rem;
    --vendor-radius-2xl: 1.5rem;
    --vendor-radius-full: 9999px;

    /* Z-Index Scale */
    --vendor-z-base: 0;
    --vendor-z-dropdown: 1000;
    --vendor-z-sticky: 1020;
    --vendor-z-fixed: 1030;
    --vendor-z-modal: 1040;
    --vendor-z-tooltip: 1050;

    /* Transitions */
    --vendor-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* === GLOBAL RESET & BASE STYLES === */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.5;
    width: 100%;
    height: 100%;
}

body {
    font-family: var(--vendor-font-primary);
    font-weight: 400;
    color: var(--vendor-text-primary);
    background-color: var(--vendor-bg);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    width: 100%;
    min-height: 100vh;
}

.vendor-body {
    display: flex;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    width: 100%;
}

/* === MODERN SIDEBAR DESIGN === */
.vendor-sidebar {
    width: var(--vendor-sidebar-width);
    background: var(--vendor-surface);
    border-right: 1px solid var(--vendor-border);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--vendor-z-fixed);
    box-shadow: var(--vendor-shadow-lg);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(20px);
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.98) 100%);
}

/* === MODERN SIDEBAR HEADER === */
.sidebar-header {
    padding: var(--vendor-space-6) var(--vendor-space-6) var(--vendor-space-8);
    border-bottom: 1px solid var(--vendor-border);
    background: transparent;
    position: relative;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    text-decoration: none;
    color: var(--vendor-text-primary);
    transition: var(--vendor-transition);
    padding: var(--vendor-space-3);
    border-radius: var(--vendor-radius-lg);
}

.sidebar-brand:hover {
    background: var(--vendor-gray-50);
}

.brand-logo {
    width: 48px;
    height: 48px;
    background: var(--vendor-primary);
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    overflow: hidden;
}



.brand-text {
    flex: 1;
    min-width: 0;
}

.brand-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    line-height: 1.2;
    margin-bottom: 2px;
}

.brand-subtitle {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

/* === MODERN NAVIGATION DESIGN === */
.sidebar-nav {
    padding: var(--vendor-space-4) var(--vendor-space-6);
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-2);
}

.nav-section {
    margin-bottom: var(--vendor-space-6);
}

.nav-section-title {
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    color: var(--vendor-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: var(--vendor-space-2) var(--vendor-space-4);
    margin-bottom: var(--vendor-space-3);
    position: relative;
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: var(--vendor-space-4);
    width: 24px;
    height: 2px;
    background: var(--vendor-primary);
    border-radius: var(--vendor-radius-full);
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-1);
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    font-weight: 500;
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition);
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    background: var(--vendor-gray-50);
    color: var(--vendor-text-primary);
}

.nav-link.active {
    background: var(--vendor-primary);
    color: white;
    box-shadow: var(--vendor-shadow-md);
}

.nav-icon {
    font-size: var(--vendor-font-size-lg);
    width: 20px;
    text-align: center;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.nav-item-text {
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.nav-badge {
    background: var(--vendor-danger);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--vendor-radius-full);
    margin-left: auto;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

/* === MAIN CONTENT WRAPPER === */
.vendor-main-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: var(--vendor-sidebar-width);
    min-height: 100vh;
    background: var(--vendor-bg);
    position: relative;
    width: calc(100vw - var(--vendor-sidebar-width));
    max-width: none;
}

/* === MODERN HEADER DESIGN === */
.vendor-header {
    background: var(--vendor-surface);
    border-bottom: 1px solid var(--vendor-border);
    height: var(--vendor-header-height);
    display: flex;
    align-items: center;
    padding: 0 var(--vendor-space-8);
    position: sticky;
    top: 0;
    z-index: var(--vendor-z-sticky);
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    width: 100%;
    min-width: 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--vendor-space-6);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-6);
    flex: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    background: none;
    border: none;
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-xl);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition);
    display: none;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-menu-toggle:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-primary);
}

/* Page Title */
.header-title {
    flex: 1;
    min-width: 0;
}

.page-title {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0;
    line-height: 1.2;
    color: var(--vendor-text-primary);
}

.page-subtitle {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 2px 0 0 0;
    line-height: 1.3;
    font-weight: 400;
}

/* === HEADER ACTIONS === */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
}

.header-action-btn {
    background: none;
    border: none;
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-lg);
    cursor: pointer;
    padding: var(--vendor-space-3);
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition);
    position: relative;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-action-btn:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-primary);
    transform: translateY(-1px);
}

.header-action-btn {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--vendor-danger);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--vendor-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--vendor-danger);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 5px;
    border-radius: var(--vendor-radius-full);
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 0 2px var(--vendor-surface);
}

/* === USER PROFILE === */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    cursor: pointer;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-xl);
    transition: var(--vendor-transition);
    border: 1px solid transparent;
}

.user-profile:hover {
    background: var(--vendor-gray-50);
    border-color: var(--vendor-border);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: var(--vendor-font-size-sm);
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    overflow: hidden;
}



.user-info {
    display: flex;
    flex-direction: column;
    text-align: left;
    min-width: 0;
}

.user-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    line-height: 1.2;
    font-weight: 500;
}

/* === MAIN CONTENT AREA === */
.vendor-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - var(--vendor-header-height));
    background: var(--vendor-bg);
    width: 100%;
}

.page-content {
    flex: 1;
    padding: var(--vendor-space-8);
    width: 100%;
    max-width: none;
    margin: 0;
    position: relative;
    box-sizing: border-box;
}

/* === MOBILE OVERLAY === */
.vendor-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--vendor-z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--vendor-transition);
    backdrop-filter: blur(4px);
}

.vendor-body.mobile-sidebar-open .vendor-mobile-overlay {
    opacity: 1;
    visibility: visible;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .vendor-sidebar {
        transform: translateX(-100%);
        z-index: calc(var(--vendor-z-modal) + 1);
        box-shadow: var(--vendor-shadow-xl);
    }

    .vendor-sidebar.mobile-open {
        transform: translateX(0);
    }

    .vendor-main-wrapper {
        margin-left: 0;
        width: 100vw;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }
}

@media (max-width: 768px) {
    .vendor-header {
        height: 64px;
        padding: 0 var(--vendor-space-4);
    }

    .header-content {
        gap: var(--vendor-space-4);
    }

    .page-content {
        padding: var(--vendor-space-6) var(--vendor-space-4);
    }

    .user-profile .user-info {
        display: none;
    }

    .page-title {
        font-size: var(--vendor-font-size-xl);
    }

    .page-subtitle {
        display: none;
    }

    .header-actions {
        gap: var(--vendor-space-2);
    }

    .header-action-btn {
        width: 40px;
        height: 40px;
        padding: var(--vendor-space-2);
    }

    .notifications-dropdown,
    .user-dropdown,
    .dropdown-menu {
        min-width: 260px;
        right: -10px;
    }

    .user-dropdown {
        min-width: 220px;
    }
}

@media (max-width: 480px) {
    .page-content {
        padding: var(--vendor-space-4) var(--vendor-space-3);
    }

    .page-title {
        font-size: var(--vendor-font-size-lg);
    }
}

/* === MODERN DROPDOWN STYLES === */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 12px);
    right: 0;
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-xl);
    box-shadow: var(--vendor-shadow-xl);
    min-width: 220px;
    z-index: var(--vendor-z-dropdown);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
    overflow: hidden;
    padding: var(--vendor-space-2) 0;
}

.dropdown-menu .dropdown-header {
    padding: var(--vendor-space-4) var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    margin: calc(-1 * var(--vendor-space-2)) 0 var(--vendor-space-2) 0;
}

.dropdown-menu .dropdown-header h3 {
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0;
}

.dropdown-header {
    padding: var(--vendor-space-4) var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    border-radius: var(--vendor-radius-xl) var(--vendor-radius-xl) 0 0;
}

.dropdown-header h3 {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-5);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    transition: var(--vendor-transition);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--vendor-gray-50);
    color: var(--vendor-text-primary);
    transform: translateX(4px);
}

.dropdown-divider {
    height: 1px;
    background: var(--vendor-border);
    margin: var(--vendor-space-2) 0;
}

/* === HEADER DROPDOWN STYLES === */
.header-action-item {
    position: relative;
    display: inline-block;
}

.notifications-dropdown,
.user-dropdown {
    position: absolute;
    top: calc(100% + 12px);
    right: 0;
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-xl);
    box-shadow: var(--vendor-shadow-xl);
    min-width: 280px;
    z-index: var(--vendor-z-dropdown);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
    overflow: hidden;
}

.user-dropdown {
    min-width: 240px;
}

.notifications-dropdown .dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-4) var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
}

.notifications-dropdown .dropdown-header h3 {
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0;
}

.notification-count {
    background: var(--vendor-primary);
    color: white;
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    padding: 2px 8px;
    border-radius: var(--vendor-radius-full);
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-4) var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border-light);
    transition: var(--vendor-transition);
    cursor: pointer;
}

.notification-item:hover {
    background: var(--vendor-gray-50);
}

.notification-item.unread {
    background: rgba(126, 217, 87, 0.02);
    border-left: 3px solid var(--vendor-primary);
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--vendor-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--vendor-primary-light);
    color: var(--vendor-primary);
    font-size: var(--vendor-font-size-sm);
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.notification-text {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.notification-time {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-light);
    margin: 0;
}

/* User Profile Button */
.user-profile-btn {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    background: none;
    border: none;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-lg);
    cursor: pointer;
    transition: var(--vendor-transition);
    color: var(--vendor-text-primary);
}

.user-profile-btn:hover {
    background: var(--vendor-gray-50);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: var(--vendor-radius-full);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--vendor-primary);
    color: white;
    font-weight: 600;
    font-size: var(--vendor-font-size-sm);
    flex-shrink: 0;
}

.user-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-avatar-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--vendor-primary);
    color: white;
    font-weight: 600;
    font-size: var(--vendor-font-size-sm);
    text-transform: uppercase;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-role {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
    line-height: 1.2;
}

.user-email {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
    line-height: 1.2;
}

.user-dropdown-icon {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    transition: var(--vendor-transition);
}

.user-profile-btn[aria-expanded="true"] .user-dropdown-icon {
    transform: rotate(180deg);
}

.user-dropdown .dropdown-header {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
}

.user-dropdown .dropdown-header .user-avatar {
    width: 48px;
    height: 48px;
}

.user-dropdown .dropdown-header .user-info {
    flex: 1;
}

.user-dropdown .dropdown-header .user-name {
    font-size: var(--vendor-font-size-base);
    max-width: none;
}

.logout-form {
    width: 100%;
}

.logout-item {
    color: var(--vendor-danger) !important;
}

.logout-item:hover {
    background: rgba(239, 68, 68, 0.1) !important;
    color: var(--vendor-danger) !important;
}

/* User Profile Button in Header */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    background: none;
    border: none;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-lg);
    cursor: pointer;
    transition: var(--vendor-transition);
    color: var(--vendor-text-primary);
}

.user-profile:hover {
    background: var(--vendor-gray-50);
}

.user-profile .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: var(--vendor-radius-full);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--vendor-primary);
    color: white;
    font-weight: 600;
    font-size: var(--vendor-font-size-sm);
    flex-shrink: 0;
    text-transform: uppercase;
}

.user-profile .user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-profile .user-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-profile .user-role {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
    line-height: 1.2;
}

.user-profile .fa-chevron-down {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    transition: var(--vendor-transition);
}

/* === UTILITY CLASSES === */
.text-primary { color: var(--vendor-primary) !important; }
.text-secondary { color: var(--vendor-text-secondary) !important; }
.text-muted { color: var(--vendor-text-muted) !important; }
.text-light { color: var(--vendor-text-light) !important; }

.bg-primary { background: var(--vendor-primary) !important; }
.bg-surface { background: var(--vendor-surface) !important; }
.bg-light { background: var(--vendor-gray-50) !important; }

.border-primary { border-color: var(--vendor-primary) !important; }
.border-light { border-color: var(--vendor-border) !important; }

.shadow-sm { box-shadow: var(--vendor-shadow-sm) !important; }
.shadow { box-shadow: var(--vendor-shadow) !important; }
.shadow-md { box-shadow: var(--vendor-shadow-md) !important; }
.shadow-lg { box-shadow: var(--vendor-shadow-lg) !important; }
.shadow-xl { box-shadow: var(--vendor-shadow-xl) !important; }

.rounded-sm { border-radius: var(--vendor-radius-sm) !important; }
.rounded { border-radius: var(--vendor-radius) !important; }
.rounded-md { border-radius: var(--vendor-radius-md) !important; }
.rounded-lg { border-radius: var(--vendor-radius-lg) !important; }
.rounded-xl { border-radius: var(--vendor-radius-xl) !important; }
.rounded-2xl { border-radius: var(--vendor-radius-2xl) !important; }
.rounded-full { border-radius: var(--vendor-radius-full) !important; }

.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.items-center { align-items: center !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }

.transition { transition: var(--vendor-transition) !important; }
.transition-fast { transition: var(--vendor-transition-fast) !important; }
.transition-slow { transition: var(--vendor-transition-slow) !important; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: var(--vendor-space-1); }
.gap-2 { gap: var(--vendor-space-2); }
.gap-3 { gap: var(--vendor-space-3); }
.gap-4 { gap: var(--vendor-space-4); }

.p-1 { padding: var(--vendor-space-1); }
.p-2 { padding: var(--vendor-space-2); }
.p-3 { padding: var(--vendor-space-3); }
.p-4 { padding: var(--vendor-space-4); }
.p-6 { padding: var(--vendor-space-6); }

.m-0 { margin: 0; }
.mb-2 { margin-bottom: var(--vendor-space-2); }
.mb-4 { margin-bottom: var(--vendor-space-4); }
.mb-6 { margin-bottom: var(--vendor-space-6); }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-xs { font-size: var(--vendor-font-size-xs); }
.text-sm { font-size: var(--vendor-font-size-sm); }
.text-base { font-size: var(--vendor-font-size-base); }
.text-lg { font-size: var(--vendor-font-size-lg); }
.text-xl { font-size: var(--vendor-font-size-xl); }

.hidden { display: none; }
.block { display: block; }

/* === ANIMATIONS === */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-fade-in { animation: fadeIn 0.3s ease-in-out; }
.animate-slide-in-right { animation: slideInRight 0.3s ease-out; }
.animate-pulse { animation: pulse 2s infinite; }
