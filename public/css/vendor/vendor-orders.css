/* ===== ULTRA MODERN VENDOR ORDERS CSS ===== */
/* Redesigned orders page with modern aesthetics matching dashboard */

/* === ALPINE.JS CLOAKING === */
[x-cloak] {
    display: none !important;
}

/* Smooth transitions for dynamic content */
.orders-table-section,
.orders-empty,
.loading-container {
    transition: opacity 0.2s ease-in-out;
}

/* Loading state styling */
.loading-container {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* === ORDERS HEADER SECTION === */
.orders-header {
    background: linear-gradient(135deg,
        rgba(144, 238, 144, 0.1) 0%,
        rgba(34, 139, 34, 0.05) 100%);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-6) var(--vendor-space-8);
    margin-bottom: var(--vendor-space-6);
    border: 1px solid var(--vendor-border);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-6);
}

.orders-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    border-radius: 50%;
    opacity: 0.1;
    transition: var(--vendor-transition);
}

.orders-header-left {
    flex: 1;
    position: relative;
    z-index: 1;
}

.orders-title {
    font-size: var(--vendor-font-size-3xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
    line-height: 1.3;
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.orders-subtitle {
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-muted);
    margin: 0;
    line-height: 1.5;
}

.orders-header-right {
    display: flex;
    gap: var(--vendor-space-3);
    position: relative;
    z-index: 1;
}

.orders-header-right .btn {
    padding: var(--vendor-space-3) var(--vendor-space-5);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    transition: var(--vendor-transition);
    border: none;
    cursor: pointer;
}

.orders-header-right .btn.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--vendor-text-primary);
    border: 1px solid var(--vendor-border);
}

.orders-header-right .btn.btn-secondary:hover {
    background: var(--vendor-gray-100);
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-md);
}

.orders-header-right .btn.btn-primary {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    color: white;
    box-shadow: var(--vendor-shadow-sm);
}

.orders-header-right .btn.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-lg);
}

/* === ORDERS STATS GRID === */
.orders-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-6);
}

.orders-stat-card {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-5);
    border: 1px solid var(--vendor-border);
    transition: var(--vendor-transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
    text-align: center;
}



.orders-stat-card.pending { --stat-color: var(--vendor-warning); }
.orders-stat-card.processing { --stat-color: var(--vendor-primary); }
.orders-stat-card.shipped { --stat-color: var(--vendor-accent); }
.orders-stat-card.delivered { --stat-color: var(--vendor-success); }
.orders-stat-card.total { --stat-color: var(--vendor-primary); }
.orders-stat-card.completed { --stat-color: var(--vendor-success); }
.orders-stat-card.failed { --stat-color: var(--vendor-danger); }

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-lg);
    color: white;
    background: linear-gradient(135deg, var(--stat-color), var(--vendor-primary-dark));
    box-shadow: var(--vendor-shadow-md);
    margin: 0 auto var(--vendor-space-4) auto;
    position: relative;
    overflow: hidden;
}



.stat-value {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 800;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
    line-height: 1;
    font-family: var(--vendor-font-mono);
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* === MODERN FILTERS SECTION === */
.orders-filters {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.filters-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--vendor-space-4);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-2);
}

.filter-label {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.search-input-container {
    position: relative;
}

.search-icon {
    position: absolute;
    left: var(--vendor-space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-sm);
}

.filter-input {
    width: 100%;
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-primary);
    background: var(--vendor-surface);
    transition: var(--vendor-transition);
}

.search-input {
    padding-left: var(--vendor-space-10);
}

.filter-input:focus {
    outline: none;
    border-color: var(--vendor-primary);
    box-shadow: 0 0 0 3px rgba(126, 217, 87, 0.1);
}

.filter-select {
    width: 100%;
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-primary);
    background: var(--vendor-surface);
    transition: var(--vendor-transition);
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--vendor-primary);
    box-shadow: 0 0 0 3px rgba(126, 217, 87, 0.1);
}

.filter-actions {
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn.btn-ghost {
    background: none;
    border: 1px solid var(--vendor-border);
    color: var(--vendor-text-muted);
    padding: var(--vendor-space-3);
    border-radius: var(--vendor-radius-lg);
    cursor: pointer;
    transition: var(--vendor-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.btn.btn-ghost:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-text-primary);
    border-color: var(--vendor-primary);
}

/* === ORDERS TABLE SECTION === */
.orders-table-section {
    margin-bottom: var(--vendor-space-8);
}

.orders-table-card {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.table-header {
    padding: var(--vendor-space-6) var(--vendor-space-8);
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0;
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.table-actions {
    display: flex;
    gap: var(--vendor-space-3);
}

.table-action-btn {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    transition: var(--vendor-transition);
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-primary);
    cursor: pointer;
}

.table-action-btn:hover {
    background: var(--vendor-gray-100);
    border-color: var(--vendor-primary);
    transform: translateY(-1px);
}

.table-action-btn.primary {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    color: white;
    border-color: transparent;
}

.table-action-btn.primary:hover {
    background: linear-gradient(135deg, var(--vendor-primary-dark), var(--vendor-primary));
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.table-body {
    padding: 0;
}

.orders-table-container {
    overflow-x: auto;
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--vendor-font-size-sm);
}

.orders-table th {
    background: var(--vendor-gray-50);
    padding: var(--vendor-space-4) var(--vendor-space-6);
    text-align: left;
    font-size: var(--vendor-font-size-xs);
    font-weight: 700;
    color: var(--vendor-text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--vendor-border);
    white-space: nowrap;
}

.orders-table td {
    padding: var(--vendor-space-4) var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-light);
    vertical-align: middle;
}

.order-row {
    transition: var(--vendor-transition);
    position: relative;
}

.order-row:hover {
    background: rgba(126, 217, 87, 0.02);
}

.order-row:last-child td {
    border-bottom: none;
}

.order-id-cell {
    font-weight: 700;
    color: var(--vendor-text-primary);
}

.order-number {
    font-family: var(--vendor-font-mono);
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.customer-cell {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
}

.customer-avatar-sm {
    width: 32px;
    height: 32px;
    border-radius: var(--vendor-radius-full);
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: var(--vendor-font-size-xs);
    text-transform: uppercase;
    flex-shrink: 0;
}

.customer-details {
    min-width: 0;
    flex: 1;
}

.customer-name {
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.customer-email {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.date-cell {
    color: var(--vendor-text-muted);
    white-space: nowrap;
}

.items-cell {
    color: var(--vendor-text-muted);
}

.items-count {
    background: var(--vendor-gray-100);
    padding: 2px 8px;
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
}

.total-cell {
    font-weight: 700;
    color: var(--vendor-text-primary);
    font-family: var(--vendor-font-mono);
    white-space: nowrap;
}

.status-cell {
    text-align: center;
}

.status-badge {
    padding: var(--vendor-space-1) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid;
    white-space: nowrap;
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--vendor-warning);
    border-color: rgba(245, 158, 11, 0.2);
}

.status-badge.processing {
    background: rgba(126, 217, 87, 0.1);
    color: var(--vendor-primary);
    border-color: rgba(126, 217, 87, 0.2);
}

.status-badge.completed {
    background: rgba(76, 175, 80, 0.1);
    color: var(--vendor-success);
    border-color: rgba(76, 175, 80, 0.2);
}

.status-badge.failed {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border-color: rgba(239, 68, 68, 0.2);
}

.actions-cell {
    display: flex;
    gap: var(--vendor-space-1);
    justify-content: center;
}

.table-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--vendor-radius-lg);
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition);
    font-size: var(--vendor-font-size-xs);
}

.table-btn:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-text-primary);
    border-color: var(--vendor-primary);
    transform: translateY(-1px);
}

.table-btn.view:hover {
    background: rgba(126, 217, 87, 0.1);
    color: var(--vendor-primary);
    border-color: var(--vendor-primary);
}

.table-btn.edit:hover {
    background: rgba(76, 175, 80, 0.1);
    color: var(--vendor-accent);
    border-color: var(--vendor-accent);
}

.table-btn.print:hover {
    background: rgba(107, 114, 128, 0.1);
    color: var(--vendor-text-primary);
    border-color: var(--vendor-text-primary);
}

.table-pagination {
    padding: var(--vendor-space-4) var(--vendor-space-6);
    border-top: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.pagination-info {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
}

.pagination-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--vendor-radius-lg);
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition);
    font-size: var(--vendor-font-size-xs);
}

.pagination-btn:hover:not(:disabled) {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-current {
    padding: var(--vendor-space-2) var(--vendor-space-3);
    background: var(--vendor-primary);
    color: white;
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    min-width: 32px;
    text-align: center;
}

/* === MODERN ORDER CARDS === */
.order-card {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-4);
    transition: var(--vendor-transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-lg);
    border-color: var(--vendor-primary);
}

.order-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--order-status-color, var(--vendor-primary)), var(--vendor-primary-dark));
    transition: var(--vendor-transition);
}

.order-card.pending { --order-status-color: var(--vendor-warning); }
.order-card.processing { --order-status-color: var(--vendor-primary); }
.order-card.shipped { --order-status-color: var(--vendor-accent); }
.order-card.delivered { --order-status-color: var(--vendor-success); }
.order-card.cancelled { --order-status-color: var(--vendor-danger); }

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--vendor-space-4);
    position: relative;
    z-index: 1;
}

.order-info {
    flex: 1;
}

.order-id {
    font-size: var(--vendor-font-size-lg);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
    line-height: 1.3;
}

.order-date {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0;
}

.order-status-badge {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid;
    backdrop-filter: blur(10px);
}

.order-status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--vendor-warning);
    border-color: rgba(245, 158, 11, 0.2);
}

.order-status-badge.processing {
    background: rgba(126, 217, 87, 0.1);
    color: var(--vendor-primary);
    border-color: rgba(126, 217, 87, 0.2);
}

.order-status-badge.shipped {
    background: rgba(76, 175, 80, 0.1);
    color: var(--vendor-accent);
    border-color: rgba(76, 175, 80, 0.2);
}

.order-status-badge.delivered {
    background: rgba(76, 175, 80, 0.1);
    color: var(--vendor-success);
    border-color: rgba(76, 175, 80, 0.2);
}

.order-status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border-color: rgba(239, 68, 68, 0.2);
}

.order-details {
    margin-bottom: var(--vendor-space-5);
}

.order-customer {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    margin-bottom: var(--vendor-space-3);
}

.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-full);
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: var(--vendor-font-size-sm);
    text-transform: uppercase;
    flex-shrink: 0;
}

.customer-info {
    flex: 1;
    min-width: 0;
}

.customer-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1) 0;
    line-height: 1.3;
}

.customer-email {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.order-items {
    background: var(--vendor-gray-50);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-4);
}

.order-items-title {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-3);
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
}

.items-count {
    background: var(--vendor-primary);
    color: white;
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    padding: 2px 8px;
    border-radius: var(--vendor-radius-full);
    line-height: 1;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--vendor-space-2) 0;
    border-bottom: 1px solid var(--vendor-border-light);
}

.order-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 500;
    color: var(--vendor-text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-quantity {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
}

.item-price {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    white-space: nowrap;
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--vendor-space-4);
    padding-top: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border);
}

.order-total {
    font-size: var(--vendor-font-size-lg);
    font-weight: 700;
    color: var(--vendor-text-primary);
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.order-actions {
    display: flex;
    gap: var(--vendor-space-2);
}

.order-action-btn {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    transition: var(--vendor-transition);
    text-decoration: none;
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-primary);
    cursor: pointer;
}

.order-action-btn:hover {
    background: var(--vendor-gray-100);
    border-color: var(--vendor-primary);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-sm);
}

.order-action-btn.primary {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    color: white;
    border-color: transparent;
}

.order-action-btn.primary:hover {
    background: linear-gradient(135deg, var(--vendor-primary-dark), var(--vendor-primary));
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

/* === EMPTY STATE === */
.orders-empty {
    text-align: center;
    padding: var(--vendor-space-12) var(--vendor-space-6);
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--vendor-radius-full);
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-3xl);
    color: white;
    margin: 0 auto var(--vendor-space-6) auto;
    box-shadow: var(--vendor-shadow-lg);
    position: relative;
    overflow: hidden;
}

.empty-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.empty-title {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-3);
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.empty-description {
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-muted);
    margin: 0 auto;
    max-width: 400px;
    line-height: 1.6;
}

/* === LOADING STATE === */
.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--vendor-border);
    border-top: 4px solid var(--vendor-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .orders-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--vendor-space-4);
    }

    .filters-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--vendor-space-3);
    }

    .orders-table-section {
        display: block;
    }

    .orders-table th,
    .orders-table td {
        padding: var(--vendor-space-3) var(--vendor-space-4);
        font-size: var(--vendor-font-size-xs);
    }

    .customer-cell {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--vendor-space-2);
    }

    .customer-avatar-sm {
        width: 28px;
        height: 28px;
    }

    .order-footer {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-3);
    }

    .order-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .orders-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-4);
        padding: var(--vendor-space-5) var(--vendor-space-6);
    }

    .orders-header-right {
        flex-direction: column;
        gap: var(--vendor-space-3);
    }

    .orders-title {
        font-size: var(--vendor-font-size-2xl);
    }

    .orders-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--vendor-space-3);
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-3);
    }

    /* Keep table visible on mobile */
    .orders-table-section {
        display: block;
        overflow-x: auto;
    }

    .table-header {
        padding: var(--vendor-space-4) var(--vendor-space-5);
    }

    .table-title {
        font-size: var(--vendor-font-size-lg);
    }

    .table-actions {
        flex-direction: column;
        gap: var(--vendor-space-2);
    }

    .order-card {
        padding: var(--vendor-space-5);
    }

    .order-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-3);
    }

    .order-customer {
        margin-bottom: var(--vendor-space-4);
    }

    .order-actions {
        flex-wrap: wrap;
        gap: var(--vendor-space-2);
    }

    .order-action-btn {
        flex: 1;
        justify-content: center;
        min-width: 0;
    }
}

@media (max-width: 640px) {
    .orders-header {
        padding: var(--vendor-space-4) var(--vendor-space-5);
    }

    .orders-title {
        font-size: var(--vendor-font-size-xl);
    }

    .orders-stats {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-3);
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-value {
        font-size: var(--vendor-font-size-xl);
    }

    .order-card {
        padding: var(--vendor-space-4);
    }

    .customer-avatar {
        width: 36px;
        height: 36px;
    }

    .order-items {
        padding: var(--vendor-space-3);
    }

    .order-actions {
        flex-direction: column;
        gap: var(--vendor-space-2);
    }

    .order-action-btn {
        padding: var(--vendor-space-3) var(--vendor-space-4);
        font-size: var(--vendor-font-size-sm);
    }

    .empty-icon {
        width: 64px;
        height: 64px;
        font-size: var(--vendor-font-size-2xl);
    }

    .empty-title {
        font-size: var(--vendor-font-size-xl);
    }
}

/* === UTILITY CLASSES === */
.d-flex {
    display: flex;
}

.justify-center {
    justify-content: center;
}

.items-center {
    align-items: center;
}

/* === ORDER MODAL === */
.order-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--vendor-space-4);
}

.order-modal {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    box-shadow: var(--vendor-shadow-xl);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: var(--vendor-space-6) var(--vendor-space-8);
    border-bottom: 1px solid var(--vendor-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--vendor-gray-50);
}

.modal-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0;
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-close-btn {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-full);
    border: none;
    background: var(--vendor-gray-200);
    color: var(--vendor-text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition);
}

.modal-close-btn:hover {
    background: var(--vendor-danger);
    color: white;
    transform: scale(1.1);
}

.modal-body {
    padding: var(--vendor-space-6) var(--vendor-space-8);
    overflow-y: auto;
    flex: 1;
}

.order-card-modal {
    background: var(--vendor-surface);
}

.order-card-modal .order-header {
    background: linear-gradient(135deg,
        rgba(144, 238, 144, 0.1) 0%,
        rgba(34, 139, 34, 0.05) 100%);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-4) var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.order-card-modal .order-info {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-1);
}

.order-card-modal .order-id {
    font-size: var(--vendor-font-size-lg);
    font-weight: 700;
    color: var(--vendor-text-primary);
    font-family: var(--vendor-font-mono);
}

.order-card-modal .order-date {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
}

.order-card-modal .order-status-badge {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.order-card-modal .order-status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--vendor-warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.order-card-modal .order-status-badge.processing {
    background: rgba(144, 238, 144, 0.1);
    color: var(--vendor-primary);
    border: 1px solid rgba(144, 238, 144, 0.2);
}

.order-card-modal .order-status-badge.completed,
.order-card-modal .order-status-badge.delivered {
    background: rgba(34, 139, 34, 0.1);
    color: var(--vendor-primary-dark);
    border: 1px solid rgba(34, 139, 34, 0.2);
}

.order-card-modal .order-status-badge.failed,
.order-card-modal .order-status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.order-card-modal .order-customer {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    padding: var(--vendor-space-4) var(--vendor-space-6);
    background: var(--vendor-gray-50);
    border-radius: var(--vendor-radius-xl);
    margin-bottom: var(--vendor-space-6);
}

.order-card-modal .customer-avatar {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-full);
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: var(--vendor-font-size-lg);
    text-transform: uppercase;
    flex-shrink: 0;
}

.order-card-modal .customer-info {
    flex: 1;
}

.order-card-modal .customer-name {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.order-card-modal .customer-email {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
}

.order-card-modal .order-items {
    margin-bottom: var(--vendor-space-6);
}

.order-card-modal .order-items-title {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-4);
    padding-bottom: var(--vendor-space-3);
    border-bottom: 1px solid var(--vendor-border);
}

.order-card-modal .items-count {
    background: var(--vendor-primary);
    color: white;
    padding: 2px 8px;
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    margin-left: auto;
}

.order-card-modal .order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-3) var(--vendor-space-4);
    background: var(--vendor-gray-50);
    border-radius: var(--vendor-radius-lg);
    margin-bottom: var(--vendor-space-3);
}

.order-card-modal .order-item:last-child {
    margin-bottom: 0;
}

.order-card-modal .item-info {
    flex: 1;
}

.order-card-modal .item-name {
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.order-card-modal .item-quantity {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
}

.order-card-modal .item-price {
    font-size: var(--vendor-font-size-base);
    font-weight: 700;
    color: var(--vendor-text-primary);
    font-family: var(--vendor-font-mono);
}

.order-card-modal .order-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--vendor-space-6);
    border-top: 1px solid var(--vendor-border);
}

.order-card-modal .order-total {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.order-card-modal .order-actions {
    display: flex;
    gap: var(--vendor-space-3);
}

.order-card-modal .order-action-btn {
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    transition: var(--vendor-transition);
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-primary);
    cursor: pointer;
}

.order-card-modal .order-action-btn:hover {
    background: var(--vendor-gray-100);
    border-color: var(--vendor-primary);
    transform: translateY(-1px);
}

.order-card-modal .order-action-btn.primary {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    color: white;
    border-color: transparent;
}

.order-card-modal .order-action-btn.primary:hover {
    background: linear-gradient(135deg, var(--vendor-primary-dark), var(--vendor-primary));
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

/* Modal responsive design */
@media (max-width: 768px) {
    .order-modal {
        max-width: 95vw;
        margin: var(--vendor-space-4);
    }

    .modal-header {
        padding: var(--vendor-space-4) var(--vendor-space-6);
    }

    .modal-body {
        padding: var(--vendor-space-4) var(--vendor-space-6);
    }

    .order-card-modal .order-footer {
        flex-direction: column;
        gap: var(--vendor-space-4);
        align-items: stretch;
    }

    .order-card-modal .order-actions {
        justify-content: center;
    }
}
