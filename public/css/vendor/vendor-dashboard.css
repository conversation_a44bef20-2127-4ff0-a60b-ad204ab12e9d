/* ===== ULTRA MODERN VENDOR DASHBOARD CSS ===== */
/* Completely redesigned dashboard with modern aesthetics */

/* === COMPACT WELCOME SECTION === */
.welcome-section {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.1) 0%,
        rgba(59, 130, 246, 0.05) 100%);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6) var(--vendor-space-8);
    margin-bottom: var(--vendor-space-6);
    border: 1px solid var(--vendor-border);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    background-color: rgba(255, 255, 255, 0.8);
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    min-height: 80px;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -30%;
    right: -10%;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
    border-radius: 50%;
    opacity: 0.06;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

.welcome-content {
    position: relative;
    z-index: 2;
    width: 100%;
}

.welcome-title {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0;
    line-height: 1.3;
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Welcome button styles removed - using simple one-liner welcome message */

/* === MODERN STATS CARDS === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-8);
    width: 100%;
    box-sizing: border-box;
}

.stats-card {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-5);
    border: 1px solid var(--vendor-border);
    transition: var(--vendor-transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--vendor-shadow-xl);
    border-color: var(--vendor-primary);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--stats-color, var(--vendor-primary)), var(--vendor-accent));
}

.stats-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100px;
    height: 100px;
    background: var(--stats-color, var(--vendor-primary));
    border-radius: 50%;
    opacity: 0.05;
    transition: var(--vendor-transition);
}

.stats-card:hover::after {
    transform: scale(1.5);
    opacity: 0.1;
}

.stats-card.sales { --stats-color: var(--vendor-primary); }
.stats-card.orders { --stats-color: var(--vendor-accent); }
.stats-card.products { --stats-color: var(--vendor-success); }
.stats-card.customers { --stats-color: var(--vendor-warning); }

.stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-4);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-lg);
    color: white;
    background: linear-gradient(135deg, var(--stats-color), var(--vendor-accent));
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    overflow: hidden;
}

.stats-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.stats-card:hover .stats-icon::before {
    transform: translateX(100%);
}

.stats-trend {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    backdrop-filter: blur(10px);
}

.stats-trend.up {
    background: rgba(16, 163, 74, 0.1);
    color: var(--vendor-success);
    border: 1px solid rgba(16, 163, 74, 0.2);
}

.stats-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.stats-value {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 800;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
    line-height: 1;
    font-family: var(--vendor-font-mono);
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--stats-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-label {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* === DASHBOARD LAYOUT === */
.dashboard-section {
    margin-bottom: var(--vendor-space-8);
}

.dashboard-section:last-child {
    margin-bottom: 0;
}

/* === MODERN RECENT ORDERS SECTION === */
.orders-section {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    overflow: hidden;
    margin-bottom: var(--vendor-space-8);
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
    width: 100%;
    box-sizing: border-box;
}

.section-header {
    padding: var(--vendor-space-6) var(--vendor-space-8);
    border-bottom: 1px solid var(--vendor-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--vendor-gray-50);
}

.section-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0;
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-action {
    color: var(--vendor-primary);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    transition: var(--vendor-transition);
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    border: 1px solid transparent;
}

.section-action:hover {
    background: var(--vendor-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th,
.orders-table td {
    padding: var(--vendor-space-5) var(--vendor-space-8);
    text-align: left;
    border-bottom: 1px solid var(--vendor-border);
}

.orders-table th {
    background: var(--vendor-gray-50);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.orders-table td {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-primary);
    font-weight: 500;
}

.orders-table tbody tr {
    transition: var(--vendor-transition);
}

.orders-table tbody tr:hover {
    background: var(--vendor-gray-50);
}

.order-id {
    font-weight: 600;
    color: var(--vendor-primary);
    font-family: var(--vendor-font-mono);
}

.order-status {
    display: inline-flex;
    align-items: center;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid;
}

.order-status.pending {
    background: rgba(217, 119, 6, 0.1);
    color: var(--vendor-warning);
    border-color: rgba(217, 119, 6, 0.2);
}

.order-status.processing {
    background: rgba(37, 99, 235, 0.1);
    color: var(--vendor-accent);
    border-color: rgba(37, 99, 235, 0.2);
}

.order-status.completed {
    background: rgba(16, 163, 74, 0.1);
    color: var(--vendor-success);
    border-color: rgba(16, 163, 74, 0.2);
}

.order-status.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border-color: rgba(239, 68, 68, 0.2);
}

/* === MODERN PAGINATION === */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-5) var(--vendor-space-8);
    background: var(--vendor-gray-50);
    border-radius: 0 0 var(--vendor-radius-2xl) var(--vendor-radius-2xl);
}

.pagination-info {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-muted);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    cursor: pointer;
    transition: var(--vendor-transition);
    font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.pagination-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
    box-shadow: var(--vendor-shadow-md);
}

/* === MODERN QUICK ACTIONS === */
.quick-actions-section {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-8);
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
    width: 100%;
    box-sizing: border-box;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--vendor-space-4);
}

.quick-action-card {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-5);
    border-radius: var(--vendor-radius-xl);
    background: var(--vendor-gray-50);
    text-decoration: none;
    color: var(--vendor-text-primary);
    transition: var(--vendor-transition);
    border: 1px solid var(--vendor-border);
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--vendor-primary), var(--vendor-accent));
    transition: width 0.3s ease;
    z-index: 0;
}

.quick-action-card:hover::before {
    width: 4px;
}

.quick-action-card:hover {
    background: var(--vendor-surface);
    border-color: var(--vendor-primary);
    transform: translateY(-4px);
    box-shadow: var(--vendor-shadow-lg);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-lg);
    color: white;
    flex-shrink: 0;
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.quick-action-card:nth-child(1) .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
}
.quick-action-card:nth-child(2) .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-accent), var(--vendor-success));
}
.quick-action-card:nth-child(3) .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-success), var(--vendor-primary));
}
.quick-action-card:nth-child(4) .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-warning), var(--vendor-danger));
}

.quick-action-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.quick-action-card:hover .quick-action-icon::before {
    transform: translateX(100%);
}

.quick-action-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.quick-action-title {
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
    line-height: 1.3;
}

.quick-action-desc {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    line-height: 1.4;
    font-weight: 400;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--vendor-space-3);
    }

    .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--vendor-space-3);
    }
}

@media (max-width: 768px) {
    .welcome-section {
        padding: var(--vendor-space-4) var(--vendor-space-6);
        min-height: 60px;
    }

    .welcome-title {
        font-size: var(--vendor-font-size-xl);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--vendor-space-3);
    }

    .orders-table th,
    .orders-table td {
        padding: var(--vendor-space-3) var(--vendor-space-4);
        font-size: var(--vendor-font-size-xs);
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }

    .section-header {
        padding: var(--vendor-space-5) var(--vendor-space-6);
    }

    .pagination {
        padding: var(--vendor-space-4) var(--vendor-space-6);
    }
}

@media (max-width: 480px) {
    .welcome-section {
        padding: var(--vendor-space-3) var(--vendor-space-4);
        min-height: 50px;
    }

    .welcome-title {
        font-size: var(--vendor-font-size-lg);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }

    .stats-card {
        padding: var(--vendor-space-4);
    }

    .stats-icon {
        width: 48px;
        height: 48px;
    }

    .orders-table th,
    .orders-table td {
        padding: var(--vendor-space-2) var(--vendor-space-3);
    }

    .quick-action-card {
        padding: var(--vendor-space-4);
    }

    .quick-action-icon {
        width: 48px;
        height: 48px;
    }
}
