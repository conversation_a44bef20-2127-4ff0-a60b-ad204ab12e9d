@extends('layouts.vendor')

@section('title', 'Orders')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-orders.css') }}">
@endpush

@section('content')
<div x-data="vendorOrders()">
    <!-- Orders Header -->
    <div class="orders-header">
        <div class="orders-header-left">
            <h1 class="orders-title">Orders Management</h1>
            <p class="orders-subtitle">Track and manage all your customer orders</p>
        </div>
        <div class="orders-header-right">
            <button class="btn btn-secondary">
                <i class="fas fa-download"></i>
                <span>Export Orders</span>
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-plus"></i>
                <span>Manual Order</span>
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="loading" class="loading-container" x-cloak>
        <div class="loading-spinner"></div>
    </div>

    <!-- Orders Stats -->
    <div class="orders-stats">
        <div class="orders-stat-card total">
            <div class="stat-icon total">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.total">0</div>
            <div class="stat-label">Total Orders</div>
        </div>

        <div class="orders-stat-card processing">
            <div class="stat-icon processing">
                <i class="fas fa-cog"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.processing">0</div>
            <div class="stat-label">Processing</div>
        </div>

        <div class="orders-stat-card completed">
            <div class="stat-icon completed">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.completed">0</div>
            <div class="stat-label">Completed Orders</div>
        </div>

        <div class="orders-stat-card failed">
            <div class="stat-icon failed">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.failed">0</div>
            <div class="stat-label">Failed Orders</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="orders-filters">
        <div class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Search Orders</label>
                <div class="search-input-container">
                    <i class="search-icon fas fa-search"></i>
                    <input type="text" x-model="searchTerm" placeholder="Search by order ID, customer name, or email..." class="filter-input search-input">
                </div>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select x-model="statusFilter" class="filter-select">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <select x-model="dateFilter" class="filter-select">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Amount</label>
                <select x-model="amountFilter" class="filter-select">
                    <option value="">All Amounts</option>
                    <option value="low">Under ₹1,000</option>
                    <option value="medium">₹1,000 - ₹10,000</option>
                    <option value="high">Above ₹10,000</option>
                </select>
            </div>

            <div class="filter-actions">
                <button @click="clearFilters()" class="btn btn-ghost">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="orders-table-section" x-show="!loading" x-cloak>
        <div class="orders-table-card">
            <div class="table-header">
                <h3 class="table-title">Recent Orders</h3>
                <div class="table-actions">
                    <button class="table-action-btn" @click="refreshOrders()">
                        <i class="fas fa-sync-alt"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="table-action-btn primary">
                        <i class="fas fa-download"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>
            <div class="table-body">
                <div class="orders-table-container">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Items</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-for="order in filteredOrders.slice(0, 10)" :key="order.id">
                                <tr class="order-row" :class="order.status">
                                    <td>
                                        <div class="order-id-cell">
                                            <span class="order-number" x-text="'#' + order.id"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="customer-cell">
                                            <div class="customer-avatar-sm" x-text="order.customerName.charAt(0).toUpperCase()"></div>
                                            <div class="customer-details">
                                                <div class="customer-name" x-text="order.customerName"></div>
                                                <div class="customer-email" x-text="order.customerEmail"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="date-cell" x-text="order.date"></div>
                                    </td>
                                    <td>
                                        <div class="items-cell">
                                            <span class="items-count" x-text="order.items.length + ' items'"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="total-cell" x-text="'₹' + order.total.toLocaleString()"></div>
                                    </td>
                                    <td>
                                        <div class="status-cell">
                                            <span class="status-badge" :class="order.status" x-text="order.status"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="actions-cell">
                                            <button class="table-btn view" @click="openOrderModal(order)" title="View Order">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="table-btn print" @click="printOrder(order)" title="Print Order">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </template>

                            <!-- No Orders Row -->
                            <tr x-show="filteredOrders.length === 0">
                                <td colspan="7" style="text-align: center; padding: 40px;">
                                    <div style="color: #666;">
                                        <i class="fas fa-shopping-cart" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                                        <p style="margin: 0; font-size: 16px;">No orders found</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Table Pagination -->
                <div class="table-pagination" x-show="filteredOrders.length > 10">
                    <div class="pagination-info">
                        <span x-text="'Showing 1-10 of ' + filteredOrders.length + ' orders'"></span>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="pagination-current">1</span>
                        <button class="pagination-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Empty State -->
    <div x-show="!loading && filteredOrders.length === 0" x-cloak class="orders-empty">
        <div class="empty-icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <h3 class="empty-title">No orders found</h3>
        <p class="empty-description">
            <span x-show="searchTerm || statusFilter || dateFilter || amountFilter">
                Try adjusting your filters to find what you're looking for.
            </span>
            <span x-show="!searchTerm && !statusFilter && !dateFilter && !amountFilter">
                Orders will appear here once customers start purchasing from your store.
            </span>
        </p>
    </div>

    <!-- Enhanced Order Details Modal -->
    <div x-show="showOrderModal" x-cloak class="enhanced-modal-overlay" @click="closeOrderModal()">
        <div class="enhanced-modal" @click.stop>
            <div class="enhanced-modal-header">
                <div class="header-left">
                    <div class="order-icon">📦</div>
                    <div class="header-info">
                        <h3>Order Details</h3>
                        <span class="order-id-header" x-text="'#' + (selectedOrder?.id || '')"></span>
                    </div>
                </div>
                <button @click="closeOrderModal()" class="enhanced-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="enhanced-modal-body" x-show="selectedOrder">
                <div class="order-content-grid">
                    <!-- Left Column - Customer & Order Info -->
                    <div class="order-info-section">
                        <div class="section-title">
                            <i class="fas fa-user"></i>
                            Customer Information
                        </div>

                        <div class="info-card">
                            <div class="info-row">
                                <span class="info-label">Customer Name</span>
                                <span class="info-value" x-text="selectedOrder?.customerName || ''"></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Email Address</span>
                                <span class="info-value" x-text="selectedOrder?.customerEmail || ''"></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Mobile Number</span>
                                <span class="info-value" x-text="selectedOrder?.customerPhone || 'N/A'"></span>
                            </div>
                        </div>

                        <div class="section-title">
                            <i class="fas fa-map-marker-alt"></i>
                            Shipping Address
                        </div>

                        <div class="info-card">
                            <div class="shipping-address" x-text="selectedOrder?.shippingAddress || 'No shipping address provided'"></div>
                        </div>

                        <div class="section-title">
                            <i class="fas fa-credit-card"></i>
                            Payment Information
                        </div>

                        <div class="info-card">
                            <div class="info-row">
                                <span class="info-label">Payment Mode</span>
                                <span class="info-value" x-text="selectedOrder?.paymentMode || 'N/A'"></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Order Date</span>
                                <span class="info-value" x-text="selectedOrder?.date || ''"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Products & Total -->
                    <div class="products-section">
                        <div class="section-title">
                            <i class="fas fa-shopping-cart"></i>
                            Order Items
                        </div>

                        <div class="products-list">
                            <template x-for="item in (selectedOrder?.items || [])" :key="item.id">
                                <div class="product-item">
                                    <div class="product-image">
                                        <img :src="item.image || '/images/default-product.jpg'" :alt="item.name" />
                                    </div>
                                    <div class="product-details">
                                        <div class="product-name" x-text="item.name"></div>
                                        <div class="product-meta">
                                            <span class="product-price" x-text="'₹' + item.price.toLocaleString()"></span>
                                            <span class="product-quantity" x-text="'Qty: ' + item.quantity"></span>
                                        </div>
                                    </div>
                                    <div class="product-total" x-text="'₹' + (item.price * item.quantity).toLocaleString()"></div>
                                </div>
                            </template>
                        </div>

                        <div class="order-summary">
                            <div class="summary-row">
                                <span class="summary-label">Subtotal</span>
                                <span class="summary-value" x-text="'₹' + ((selectedOrder?.total || 0) * 0.85).toFixed(0)"></span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Shipping</span>
                                <span class="summary-value">₹50</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Tax</span>
                                <span class="summary-value" x-text="'₹' + ((selectedOrder?.total || 0) * 0.15).toFixed(0)"></span>
                            </div>
                            <div class="summary-row total-row">
                                <span class="summary-label">Total Amount</span>
                                <span class="summary-value total-amount" x-text="'₹' + ((selectedOrder?.total || 0).toLocaleString())"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions Footer -->
                <div class="modal-footer">
                    <div class="status-section">
                        <span class="current-status">Status: </span>
                        <span class="status-badge" :class="selectedOrder?.status" x-text="selectedOrder?.status || ''"></span>
                    </div>
                    <div class="action-buttons">
                        <button @click="updateOrderStatus(selectedOrder)" class="enhanced-btn primary">
                            <i class="fas fa-edit"></i>
                            Update Status
                        </button>
                        <button @click="printOrder(selectedOrder)" class="enhanced-btn secondary">
                            <i class="fas fa-print"></i>
                            Print Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('page-scripts')
<script>
function vendorOrders() {
    return {
        loading: true,
        orders: [],
        searchTerm: '',
        statusFilter: '',
        dateFilter: '',
        amountFilter: '',
        showOrderModal: false,
        selectedOrder: null,

        init() {
            console.log('Vendor Orders initialized');
            // Set loading to false immediately to show content
            this.loading = false;
            this.fetchData();
        },

        fetchData() {
            console.log('Fetching data...');
            this.loading = true;
            setTimeout(() => {
                const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
                const customers = [
                    {
                        name: 'Rajesh Kumar',
                        email: '<EMAIL>',
                        phone: '+91 98765 43210',
                        address: '123 MG Road, Sector 15, Gurgaon, Haryana 122001'
                    },
                    {
                        name: 'Priya Sharma',
                        email: '<EMAIL>',
                        phone: '+91 87654 32109',
                        address: '456 Park Street, Connaught Place, New Delhi 110001'
                    },
                    {
                        name: 'Amit Singh',
                        email: '<EMAIL>',
                        phone: '+91 76543 21098',
                        address: '789 Brigade Road, Bangalore, Karnataka 560025'
                    },
                    {
                        name: 'Sunita Patel',
                        email: '<EMAIL>',
                        phone: '+91 65432 10987',
                        address: '321 Marine Drive, Mumbai, Maharashtra 400020'
                    },
                    {
                        name: 'Vikram Gupta',
                        email: '<EMAIL>',
                        phone: '+91 54321 09876',
                        address: '654 Sector 17, Chandigarh, Punjab 160017'
                    }
                ];

                const paymentModes = ['Credit Card', 'Debit Card', 'UPI', 'Net Banking', 'Cash on Delivery'];

                const productNames = [
                    'Wireless Bluetooth Headphones',
                    'Smart Fitness Watch',
                    'Portable Power Bank',
                    'Wireless Mouse',
                    'USB-C Cable',
                    'Phone Case',
                    'Laptop Stand',
                    'Bluetooth Speaker',
                    'Screen Protector',
                    'Car Charger'
                ];

                const productImages = [
                    'https://images.unsplash.com/photo-*************-5e560c06d30e?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-*************-37898b6baf30?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-*************-b43bada2e3c9?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-*************-7fd91fc51a46?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-*************-7fd91fc51a46?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=100&h=100&fit=crop',
                    'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=100&h=100&fit=crop'
                ];

                this.orders = Array.from({ length: 25 }, (_, i) => {
                    const customer = customers[i % customers.length];
                    const status = statuses[i % statuses.length];
                    const itemCount = Math.floor(Math.random() * 5) + 1;

                    return {
                        id: `WM${2024}${String(i + 1).padStart(3, '0')}`,
                        customerName: customer.name,
                        customerEmail: customer.email,
                        customerPhone: customer.phone,
                        shippingAddress: customer.address,
                        paymentMode: paymentModes[i % paymentModes.length],
                        date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString('en-IN'),
                        total: Math.floor(500 + Math.random() * 15000),
                        status: status,
                        items: Array.from({ length: itemCount }, (_, j) => ({
                            id: j + 1,
                            name: productNames[j % productNames.length],
                            quantity: Math.floor(Math.random() * 3) + 1,
                            price: Math.floor(200 + Math.random() * 2000),
                            image: productImages[j % productImages.length]
                        }))
                    };
                });
                this.loading = false;
                console.log('Orders loaded:', this.orders.length);
            }, 500);
        },

        get filteredOrders() {
            if (!this.orders || this.orders.length === 0) {
                return [];
            }

            return this.orders.filter(order => {
                const searchMatch = !this.searchTerm ||
                    order.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    order.customerName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    order.customerEmail.toLowerCase().includes(this.searchTerm.toLowerCase());

                const statusMatch = !this.statusFilter || order.status === this.statusFilter;

                let amountMatch = true;
                if (this.amountFilter === 'low') amountMatch = order.total < 1000;
                else if (this.amountFilter === 'medium') amountMatch = order.total >= 1000 && order.total <= 10000;
                else if (this.amountFilter === 'high') amountMatch = order.total > 10000;

                return searchMatch && statusMatch && amountMatch;
            });
        },

        get statusCounts() {
            if (!this.orders || this.orders.length === 0) {
                return {
                    total: 0,
                    pending: 0,
                    processing: 0,
                    shipped: 0,
                    delivered: 0,
                    cancelled: 0,
                    completed: 0,
                    failed: 0
                };
            }

            const counts = this.orders.reduce((acc, order) => {
                acc[order.status] = (acc[order.status] || 0) + 1;
                return acc;
            }, { pending: 0, processing: 0, shipped: 0, delivered: 0, cancelled: 0, completed: 0, failed: 0 });

            // Calculate total and map statuses
            counts.total = this.orders.length;
            counts.completed = (counts.delivered || 0) + (counts.shipped || 0);
            counts.failed = counts.cancelled || 0;

            return counts;
        },

        clearFilters() {
            this.searchTerm = '';
            this.statusFilter = '';
            this.dateFilter = '';
            this.amountFilter = '';
        },

        updateOrderStatus(order) {
            // Show status update modal or inline editor
            const newStatus = prompt('Enter new status (pending, processing, shipped, delivered, cancelled):', order.status);
            if (newStatus && ['pending', 'processing', 'shipped', 'delivered', 'cancelled'].includes(newStatus)) {
                order.status = newStatus;
                window.showToast(`Order ${order.id} status updated to ${newStatus}`, 'success');
            }
        },

        printOrder(order) {
            // Print order functionality
            window.print();
            window.showToast(`Printing order ${order.id}`, 'info');
        },

        refreshOrders() {
            this.loading = true;
            // Simulate API call
            setTimeout(() => {
                this.fetchData();
                window.showToast('Orders refreshed successfully', 'success');
            }, 1000);
        },

        viewOrder(order) {
            // Navigate to order details page
            window.location.href = `/vendor/orders/${order.id}`;
        },

        openOrderModal(order) {
            this.selectedOrder = order;
            this.showOrderModal = true;
            // Prevent body scroll when modal is open
            document.body.style.overflow = 'hidden';
        },

        closeOrderModal() {
            this.showOrderModal = false;
            this.selectedOrder = null;
            // Restore body scroll
            document.body.style.overflow = '';
        }
    }
}
</script>
@endpush
@endsection

