@extends('layouts.vendor')

@section('title', 'Orders')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-orders.css') }}">
@endpush

@section('content')
<div x-data="vendorOrders()">
    <!-- Orders Header -->
    <div class="orders-header">
        <div class="orders-header-left">
            <h1 class="orders-title">Orders Management</h1>
            <p class="orders-subtitle">Track and manage all your customer orders</p>
        </div>
        <div class="orders-header-right">
            <button class="btn btn-secondary">
                <i class="fas fa-download"></i>
                <span>Export Orders</span>
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-plus"></i>
                <span>Manual Order</span>
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <template x-if="loading">
        <div class="d-flex justify-center items-center" style="min-height: 400px;">
            <div class="loading-spinner"></div>
        </div>
    </template>

    <!-- Orders Stats -->
    <div class="orders-stats" x-show="!loading">
        <div class="orders-stat-card">
            <div class="stat-icon pending">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.pending">12</div>
            <div class="stat-label">Pending Orders</div>
        </div>

        <div class="orders-stat-card">
            <div class="stat-icon processing">
                <i class="fas fa-cog"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.processing">8</div>
            <div class="stat-label">Processing</div>
        </div>

        <div class="orders-stat-card">
            <div class="stat-icon shipped">
                <i class="fas fa-shipping-fast"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.shipped">15</div>
            <div class="stat-label">Shipped</div>
        </div>

        <div class="orders-stat-card">
            <div class="stat-icon delivered">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-value" x-text="statusCounts.delivered">45</div>
            <div class="stat-label">Delivered</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="orders-filters" x-show="!loading">
        <div class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Search Orders</label>
                <div class="search-input-container">
                    <i class="search-icon fas fa-search"></i>
                    <input type="text" x-model="searchTerm" placeholder="Search by order ID, customer name, or email..." class="filter-input search-input">
                </div>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select x-model="statusFilter" class="filter-select">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <select x-model="dateFilter" class="filter-select">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Amount</label>
                <select x-model="amountFilter" class="filter-select">
                    <option value="">All Amounts</option>
                    <option value="low">Under ₹1,000</option>
                    <option value="medium">₹1,000 - ₹10,000</option>
                    <option value="high">Above ₹10,000</option>
                </select>
            </div>

            <div class="filter-actions">
                <button @click="clearFilters()" class="btn btn-ghost">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Orders List -->
    <div x-show="!loading && filteredOrders.length > 0">
        <template x-for="order in filteredOrders" :key="order.id">
            <div class="order-card">
                <!-- Order Header -->
                <div class="order-header">
                    <div class="order-info">
                        <div class="order-id" x-text="'Order #' + order.id"></div>
                        <div class="order-date" x-text="order.date"></div>
                    </div>
                    <div class="order-status-badge" :class="order.status">
                        <span x-text="order.status"></span>
                    </div>
                </div>

                <!-- Customer Info -->
                <div class="order-customer">
                    <div class="customer-avatar" x-text="order.customerName.charAt(0).toUpperCase()"></div>
                    <div class="customer-info">
                        <div class="customer-name" x-text="order.customerName"></div>
                        <div class="customer-email" x-text="order.customerEmail"></div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="order-items">
                    <div class="order-items-title">
                        <i class="fas fa-box"></i>
                        <span>Order Items</span>
                        <span class="items-count" x-text="order.items.length"></span>
                    </div>
                    <template x-for="item in order.items.slice(0, 3)" :key="item.id">
                        <div class="order-item">
                            <div class="item-info">
                                <div class="item-name" x-text="item.name"></div>
                                <div class="item-quantity" x-text="'Qty: ' + item.quantity"></div>
                            </div>
                            <div class="item-price" x-text="'₹' + item.price.toLocaleString()"></div>
                        </div>
                    </template>
                    <div x-show="order.items.length > 3" class="order-item">
                        <div class="item-info">
                            <div class="item-name" x-text="'+ ' + (order.items.length - 3) + ' more items'"></div>
                            <div class="item-quantity">View all items</div>
                        </div>
                        <div class="item-price">...</div>
                    </div>
                </div>

                <!-- Order Footer -->
                <div class="order-footer">
                    <div class="order-total" x-text="'Total: ₹' + order.total.toLocaleString()"></div>
                    <div class="order-actions">
                        <a :href="'/vendor/orders/' + order.id" class="order-action-btn">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </a>
                        <button @click="updateOrderStatus(order)" class="order-action-btn primary">
                            <i class="fas fa-edit"></i>
                            <span>Update</span>
                        </button>
                        <button @click="printOrder(order)" class="order-action-btn">
                            <i class="fas fa-print"></i>
                            <span>Print</span>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Empty State -->
    <div x-show="!loading && filteredOrders.length === 0" class="orders-empty">
        <div class="empty-icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <h3 class="empty-title">No orders found</h3>
        <p class="empty-description">
            <span x-show="searchTerm || statusFilter || dateFilter || amountFilter">
                Try adjusting your filters to find what you're looking for.
            </span>
            <span x-show="!searchTerm && !statusFilter && !dateFilter && !amountFilter">
                Orders will appear here once customers start purchasing from your store.
            </span>
        </p>
    </div>
</div>

@push('scripts')
<script>
function vendorOrders() {
    return {
        loading: true,
        orders: [],
        searchTerm: '',
        statusFilter: '',
        dateFilter: '',
        amountFilter: '',

        init() {
            this.fetchData();
        },

        fetchData() {
            this.loading = true;
            setTimeout(() => {
                const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
                const customers = [
                    { name: 'Rajesh Kumar', email: '<EMAIL>' },
                    { name: 'Priya Sharma', email: '<EMAIL>' },
                    { name: 'Amit Singh', email: '<EMAIL>' },
                    { name: 'Sunita Patel', email: '<EMAIL>' },
                    { name: 'Vikram Gupta', email: '<EMAIL>' }
                ];

                this.orders = Array.from({ length: 25 }, (_, i) => {
                    const customer = customers[i % customers.length];
                    const status = statuses[i % statuses.length];
                    const itemCount = Math.floor(Math.random() * 5) + 1;

                    return {
                        id: `WM${2024}${String(i + 1).padStart(3, '0')}`,
                        customerName: customer.name,
                        customerEmail: customer.email,
                        date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString('en-IN'),
                        total: Math.floor(500 + Math.random() * 15000),
                        status: status,
                        items: Array.from({ length: itemCount }, (_, j) => ({
                            id: j + 1,
                            name: `Product ${j + 1}`,
                            quantity: Math.floor(Math.random() * 3) + 1,
                            price: Math.floor(200 + Math.random() * 2000)
                        }))
                    };
                });
                this.loading = false;
            }, 1000);
        },

        get filteredOrders() {
            return this.orders.filter(order => {
                const searchMatch = !this.searchTerm ||
                    order.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    order.customerName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    order.customerEmail.toLowerCase().includes(this.searchTerm.toLowerCase());

                const statusMatch = !this.statusFilter || order.status === this.statusFilter;

                let amountMatch = true;
                if (this.amountFilter === 'low') amountMatch = order.total < 1000;
                else if (this.amountFilter === 'medium') amountMatch = order.total >= 1000 && order.total <= 10000;
                else if (this.amountFilter === 'high') amountMatch = order.total > 10000;

                return searchMatch && statusMatch && amountMatch;
            });
        },

        get statusCounts() {
            return this.orders.reduce((acc, order) => {
                acc[order.status] = (acc[order.status] || 0) + 1;
                return acc;
            }, { pending: 0, processing: 0, shipped: 0, delivered: 0, cancelled: 0 });
        },

        clearFilters() {
            this.searchTerm = '';
            this.statusFilter = '';
            this.dateFilter = '';
            this.amountFilter = '';
        },

        updateOrderStatus(order) {
            // Show status update modal or inline editor
            const newStatus = prompt('Enter new status (pending, processing, shipped, delivered, cancelled):', order.status);
            if (newStatus && ['pending', 'processing', 'shipped', 'delivered', 'cancelled'].includes(newStatus)) {
                order.status = newStatus;
                window.showToast(`Order ${order.id} status updated to ${newStatus}`, 'success');
            }
        },

        printOrder(order) {
            // Print order functionality
            window.print();
            window.showToast(`Printing order ${order.id}`, 'info');
        }
    }
}
</script>
@endpush
@endsection

